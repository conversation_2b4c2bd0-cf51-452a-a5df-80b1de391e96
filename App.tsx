// FinTranzo - 智能個人財務管理應用
import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text, ActivityIndicator } from 'react-native';
import AppNavigator from './src/navigation/AppNavigator';
import { AuthProvider } from './src/contexts/AuthContext';
import { supabase } from './src/services/supabase';

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    console.log('🚀 FinTranzo 應用啟動');
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      console.log('🔧 初始化應用...');

      // 測試 Supabase 連接
      const { data, error } = await supabase.from('assets').select('count').limit(1);
      if (error) {
        console.warn('⚠️ Supabase 連接測試失敗:', error.message);
      } else {
        console.log('✅ Supabase 連接正常');
      }

      setIsLoading(false);
    } catch (error) {
      console.error('❌ 應用初始化失敗:', error);
      setInitError(error instanceof Error ? error.message : '未知錯誤');
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <SafeAreaProvider>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8f9fa' }}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={{ marginTop: 16, fontSize: 16, color: '#666' }}>正在啟動 FinTranzo...</Text>
        </View>
      </SafeAreaProvider>
    );
  }

  if (initError) {
    return (
      <SafeAreaProvider>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#f8f9fa', padding: 20 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#d32f2f', marginBottom: 10 }}>初始化失敗</Text>
          <Text style={{ fontSize: 14, color: '#666', textAlign: 'center' }}>{initError}</Text>
          <Text style={{ fontSize: 12, color: '#999', marginTop: 10, textAlign: 'center' }}>
            應用將以離線模式運行
          </Text>
        </View>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <StatusBar style="auto" />
        <AppNavigator />
      </AuthProvider>
    </SafeAreaProvider>
  );
}


