// 超級簡化測試版本 - 測試 iOS 模擬器顯示
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export default function App() {
  console.log('🚀 超級簡化測試版本啟動');

  return (
    <View style={styles.container}>
      <Text style={styles.title}>✅ FinTranzo 正在運行！</Text>
      <Text style={styles.subtitle}>沒有閃退 - 應用正常工作</Text>
      <Text style={styles.info}>iOS 模擬器測試成功</Text>
      <Text style={styles.success}>🎉 恭喜！應用已成功運行</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f8ff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#2e7d32',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 15,
    textAlign: 'center',
    color: '#1976d2',
  },
  info: {
    fontSize: 16,
    marginBottom: 15,
    textAlign: 'center',
    color: '#666',
  },
  success: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#ff6f00',
  },
});
