#!/bin/bash

# 🔄 FinTranzo 智能版本切換器
# 用途: 在最小化版本和完整版本之間安全切換，避免閃退問題

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

PROJECT_PATH="/Users/<USER>/Documents/開發app/FinTranzo"

echo -e "${PURPLE}🔄 FinTranzo 智能版本切換器${NC}"
echo -e "${PURPLE}================================${NC}"

# 進入項目目錄
cd "$PROJECT_PATH" || exit 1

# 檢查當前版本
check_current_version() {
    if [ ! -f "App.tsx" ]; then
        echo -e "${RED}❌ App.tsx 不存在${NC}"
        exit 1
    fi
    
    if grep -q "最小化測試版本" App.tsx; then
        echo -e "${YELLOW}📱 當前版本: 最小化測試版本${NC}"
        return 1  # 最小化版本
    elif grep -q "ErrorBoundary" App.tsx; then
        echo -e "${GREEN}📱 當前版本: 完整版本（帶錯誤邊界）${NC}"
        return 2  # 完整版本
    else
        echo -e "${BLUE}📱 當前版本: 標準版本${NC}"
        return 0  # 標準版本
    fi
}

# 備份當前版本
backup_current_version() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_name="App_backup_$timestamp.tsx"
    
    cp App.tsx "$backup_name"
    echo -e "${GREEN}✅ 已備份當前版本到: $backup_name${NC}"
}

# 切換到最小化版本
switch_to_minimal() {
    echo -e "${CYAN}🔄 切換到最小化測試版本...${NC}"
    
    if [ -f "App_minimal.tsx" ]; then
        backup_current_version
        cp App_minimal.tsx App.tsx
        echo -e "${GREEN}✅ 已切換到最小化版本${NC}"
        echo -e "${YELLOW}💡 這個版本只顯示基本文字，用於測試是否閃退${NC}"
    else
        echo -e "${RED}❌ App_minimal.tsx 不存在，正在創建...${NC}"
        create_minimal_version
    fi
}

# 切換到完整版本
switch_to_full() {
    echo -e "${CYAN}🔄 切換到完整功能版本...${NC}"
    
    if [ -f "App_backup.tsx" ]; then
        backup_current_version
        cp App_backup.tsx App.tsx
        echo -e "${GREEN}✅ 已切換到完整版本${NC}"
        echo -e "${YELLOW}💡 這個版本包含所有功能和錯誤邊界保護${NC}"
    else
        echo -e "${RED}❌ App_backup.tsx 不存在${NC}"
        echo -e "${YELLOW}⚠️ 無法切換到完整版本${NC}"
    fi
}

# 創建最小化版本
create_minimal_version() {
    cat > App_minimal.tsx << 'EOF'
// 最小化測試版本 - 只顯示基本文字，測試 iOS 閃退問題
import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

export default function App() {
  console.log('🚀 最小化測試版本啟動');
  
  return (
    <SafeAreaProvider>
      <View style={styles.container}>
        <StatusBar style="auto" />
        <Text style={styles.title}>FinTranzo 測試版本</Text>
        <Text style={styles.subtitle}>如果你看到這個畫面，表示應用沒有閃退</Text>
        <Text style={styles.info}>這是最小化測試版本</Text>
      </View>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
    color: '#666',
  },
  info: {
    fontSize: 14,
    textAlign: 'center',
    color: '#999',
  },
});
EOF
    
    echo -e "${GREEN}✅ 已創建最小化版本${NC}"
    cp App_minimal.tsx App.tsx
}

# 測試當前版本
test_current_version() {
    echo -e "${CYAN}🧪 測試當前版本...${NC}"
    echo -e "${YELLOW}⏳ 正在啟動測試...${NC}"
    
    # 創建測試腳本
    cat > test_version.sh << 'EOF'
#!/bin/bash
timeout 30s npx expo start --ios --no-dev --minify 2>&1 | tee test_output.log &
TEST_PID=$!
sleep 10
if ps -p $TEST_PID > /dev/null; then
    echo "✅ 應用啟動成功"
    kill $TEST_PID 2>/dev/null || true
    exit 0
else
    echo "❌ 應用啟動失敗"
    exit 1
fi
EOF
    
    chmod +x test_version.sh
    
    if ./test_version.sh; then
        echo -e "${GREEN}✅ 當前版本測試通過${NC}"
        rm -f test_version.sh test_output.log
        return 0
    else
        echo -e "${RED}❌ 當前版本測試失敗${NC}"
        if [ -f "test_output.log" ]; then
            echo -e "${YELLOW}📋 錯誤日誌:${NC}"
            tail -10 test_output.log
        fi
        rm -f test_version.sh test_output.log
        return 1
    fi
}

# 智能推薦
smart_recommendation() {
    echo -e "${BLUE}🤖 智能分析和推薦...${NC}"
    
    # 檢查是否有錯誤日誌
    if [ -f "test_output.log" ] && grep -q -i "error\|crash\|fail" test_output.log; then
        echo -e "${RED}⚠️ 檢測到錯誤，建議使用最小化版本${NC}"
        return 1
    fi
    
    # 檢查依賴完整性
    if ! npm ls --depth=0 >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️ 依賴有問題，建議使用最小化版本${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ 系統狀態良好，可以使用完整版本${NC}"
    return 0
}

# 主菜單
show_menu() {
    echo ""
    echo -e "${CYAN}請選擇操作:${NC}"
    echo -e "${BLUE}1) 切換到最小化版本（安全測試）${NC}"
    echo -e "${BLUE}2) 切換到完整功能版本${NC}"
    echo -e "${BLUE}3) 測試當前版本${NC}"
    echo -e "${BLUE}4) 智能推薦${NC}"
    echo -e "${BLUE}5) 查看當前版本狀態${NC}"
    echo -e "${BLUE}6) 退出${NC}"
    echo ""
    read -p "請輸入選項 (1-6): " choice
}

# 主程序
main() {
    # 檢查當前版本
    check_current_version
    current_version=$?
    
    while true; do
        show_menu
        
        case $choice in
            1)
                switch_to_minimal
                echo -e "${CYAN}💡 建議運行: npx expo start --ios${NC}"
                ;;
            2)
                if smart_recommendation; then
                    switch_to_full
                    echo -e "${CYAN}💡 建議運行: npx expo start --ios${NC}"
                else
                    echo -e "${YELLOW}⚠️ 系統建議先使用最小化版本測試${NC}"
                    read -p "是否仍要切換到完整版本？(y/N): " confirm
                    if [[ $confirm =~ ^[Yy]$ ]]; then
                        switch_to_full
                    fi
                fi
                ;;
            3)
                test_current_version
                ;;
            4)
                if smart_recommendation; then
                    echo -e "${GREEN}💡 推薦使用完整功能版本${NC}"
                else
                    echo -e "${YELLOW}💡 推薦使用最小化測試版本${NC}"
                fi
                ;;
            5)
                check_current_version
                ;;
            6)
                echo -e "${GREEN}👋 再見！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 無效選項，請重新選擇${NC}"
                ;;
        esac
        
        echo ""
        read -p "按 Enter 繼續..."
    done
}

# 運行主程序
main
