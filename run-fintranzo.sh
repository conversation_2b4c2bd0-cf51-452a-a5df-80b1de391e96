#!/bin/bash

# 🚀 FinTranzo 一鍵運行腳本
# 用途: 全自動化檢查、設置、測試和運行 FinTranzo 應用

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

PROJECT_PATH="/Users/<USER>/Documents/開發app/FinTranzo"

# ASCII Art Logo
echo -e "${PURPLE}"
cat << 'EOF'
 _____ _       _____                        
|  ___(_)     |_   _|                       
| |_   _ _ __   | |_ __ __ _ _ __  ____  ___  
|  _| | | '_ \  | | '__/ _` | '_ \|_  / / _ \ 
| |   | | | | | | | | | (_| | | | |/ / | (_) |
\_|   |_|_| |_| \_/_|  \__,_|_| |_/___| \___/ 
                                             
🚀 全自動化 iOS 模擬器運行系統
EOF
echo -e "${NC}"

echo -e "${CYAN}📅 啟動時間: $(date)${NC}"
echo -e "${CYAN}📂 項目路徑: $PROJECT_PATH${NC}"
echo ""

# 函數：檢查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 函數：安全執行命令
safe_execute() {
    echo -e "${YELLOW}🔧 執行: $1${NC}"
    if eval "$1"; then
        echo -e "${GREEN}✅ 成功${NC}"
        return 0
    else
        echo -e "${RED}❌ 失敗${NC}"
        return 1
    fi
}

# 步驟 1: 環境檢查
echo -e "${BLUE}🔍 步驟 1: 環境檢查${NC}"
echo -e "${BLUE}========================${NC}"

# 檢查 macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}❌ 此腳本僅支持 macOS${NC}"
    exit 1
fi

echo -e "${GREEN}✅ macOS 系統檢查通過${NC}"

# 檢查 Xcode
if ! xcode-select -p >/dev/null 2>&1; then
    echo -e "${RED}❌ Xcode 未安裝或未配置${NC}"
    echo -e "${YELLOW}請先安裝 Xcode 並運行: sudo xcode-select --install${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Xcode 檢查通過${NC}"

# 步驟 2: 依賴檢查和安裝
echo -e "${BLUE}📦 步驟 2: 依賴檢查和安裝${NC}"
echo -e "${BLUE}=============================${NC}"

# 檢查並安裝 Homebrew
if ! command_exists brew; then
    echo -e "${YELLOW}🔧 安裝 Homebrew...${NC}"
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # 添加到 PATH
    if [[ -f "/opt/homebrew/bin/brew" ]]; then
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
fi

echo -e "${GREEN}✅ Homebrew 準備就緒${NC}"

# 檢查並安裝 Node.js
if ! command_exists node; then
    echo -e "${YELLOW}🔧 安裝 Node.js...${NC}"
    safe_execute "brew install node"
fi

node_version=$(node --version)
echo -e "${GREEN}✅ Node.js $node_version 準備就緒${NC}"

# 檢查並安裝 Expo CLI
if ! command_exists expo; then
    echo -e "${YELLOW}🔧 安裝 Expo CLI...${NC}"
    safe_execute "npm install -g @expo/cli"
fi

expo_version=$(expo --version)
echo -e "${GREEN}✅ Expo CLI $expo_version 準備就緒${NC}"

# 步驟 3: 項目設置
echo -e "${BLUE}📱 步驟 3: 項目設置${NC}"
echo -e "${BLUE}=====================${NC}"

# 進入項目目錄
cd "$PROJECT_PATH" || {
    echo -e "${RED}❌ 無法進入項目目錄${NC}"
    exit 1
}

echo -e "${GREEN}✅ 進入項目目錄: $(pwd)${NC}"

# 安裝項目依賴
if [ ! -d "node_modules" ] || [ ! -f "package-lock.json" ]; then
    echo -e "${YELLOW}📦 安裝項目依賴...${NC}"
    safe_execute "npm install"
else
    echo -e "${GREEN}✅ 依賴已安裝${NC}"
fi

# 步驟 4: 健康檢查
echo -e "${BLUE}🏥 步驟 4: 應用健康檢查${NC}"
echo -e "${BLUE}===========================${NC}"

# 運行健康檢查腳本
if [ -f "app-health-check.sh" ]; then
    chmod +x app-health-check.sh
    ./app-health-check.sh
else
    echo -e "${YELLOW}⚠️ 健康檢查腳本不存在，跳過詳細檢查${NC}"
fi

# 步驟 5: 版本選擇
echo -e "${BLUE}🔄 步驟 5: 版本選擇${NC}"
echo -e "${BLUE}===================${NC}"

# 檢查當前版本
if grep -q "最小化測試版本" App.tsx 2>/dev/null; then
    echo -e "${YELLOW}📱 當前使用最小化測試版本${NC}"
    current_version="minimal"
elif grep -q "ErrorBoundary" App.tsx 2>/dev/null; then
    echo -e "${GREEN}📱 當前使用完整功能版本${NC}"
    current_version="full"
else
    echo -e "${BLUE}📱 當前使用標準版本${NC}"
    current_version="standard"
fi

# 智能版本推薦
echo -e "${CYAN}🤖 智能版本推薦...${NC}"

# 檢查是否是首次運行
if [ ! -f ".fintranzo_run_history" ]; then
    echo -e "${YELLOW}🆕 檢測到首次運行，建議使用最小化版本進行測試${NC}"
    recommended_version="minimal"
    echo "first_run=$(date)" > .fintranzo_run_history
else
    # 檢查上次運行是否成功
    if grep -q "last_success" .fintranzo_run_history; then
        echo -e "${GREEN}✅ 上次運行成功，可以使用完整版本${NC}"
        recommended_version="full"
    else
        echo -e "${YELLOW}⚠️ 上次運行可能有問題，建議使用最小化版本${NC}"
        recommended_version="minimal"
    fi
fi

# 版本選擇菜單
echo ""
echo -e "${CYAN}請選擇要運行的版本:${NC}"
echo -e "${BLUE}1) 最小化測試版本 (推薦首次運行)${NC}"
echo -e "${BLUE}2) 完整功能版本${NC}"
echo -e "${BLUE}3) 使用當前版本${NC}"
echo -e "${BLUE}4) 智能推薦 ($recommended_version)${NC}"
echo ""

read -p "請輸入選項 (1-4) [默認: 4]: " version_choice
version_choice=${version_choice:-4}

case $version_choice in
    1)
        if [ -f "App_minimal.tsx" ]; then
            cp App_minimal.tsx App.tsx
            echo -e "${GREEN}✅ 已切換到最小化版本${NC}"
        fi
        ;;
    2)
        if [ -f "App_backup.tsx" ]; then
            cp App_backup.tsx App.tsx
            echo -e "${GREEN}✅ 已切換到完整版本${NC}"
        else
            echo -e "${YELLOW}⚠️ 完整版本不存在，使用當前版本${NC}"
        fi
        ;;
    3)
        echo -e "${BLUE}ℹ️ 使用當前版本${NC}"
        ;;
    4)
        if [ "$recommended_version" = "minimal" ] && [ -f "App_minimal.tsx" ]; then
            cp App_minimal.tsx App.tsx
            echo -e "${GREEN}✅ 已切換到推薦的最小化版本${NC}"
        elif [ "$recommended_version" = "full" ] && [ -f "App_backup.tsx" ]; then
            cp App_backup.tsx App.tsx
            echo -e "${GREEN}✅ 已切換到推薦的完整版本${NC}"
        fi
        ;;
    *)
        echo -e "${YELLOW}⚠️ 無效選項，使用當前版本${NC}"
        ;;
esac

# 步驟 6: iOS 模擬器準備
echo -e "${BLUE}📱 步驟 6: iOS 模擬器準備${NC}"
echo -e "${BLUE}===========================${NC}"

# 檢查可用的模擬器
echo -e "${CYAN}🔍 檢查可用的 iOS 模擬器...${NC}"
available_simulators=$(xcrun simctl list devices available | grep -E "iPhone|iPad" | head -5)

if [ -z "$available_simulators" ]; then
    echo -e "${YELLOW}⚠️ 沒有找到可用的模擬器${NC}"
    echo -e "${CYAN}💡 請在 Xcode 中下載 iOS 模擬器，或運行以下命令創建:${NC}"
    echo -e "${BLUE}   xcrun simctl create 'iPhone 15' 'iPhone 15' 'iOS-17-0'${NC}"
else
    echo -e "${GREEN}✅ 找到可用的模擬器${NC}"
    echo "$available_simulators" | head -3
fi

# 步驟 7: 啟動應用
echo -e "${BLUE}🚀 步驟 7: 啟動應用${NC}"
echo -e "${BLUE}===================${NC}"

echo -e "${PURPLE}🎉 準備啟動 FinTranzo！${NC}"
echo -e "${CYAN}📱 將在 iOS 模擬器中運行應用${NC}"
echo ""

# 創建啟動命令
start_command="npx expo start --ios --clear"

echo -e "${YELLOW}⏳ 正在啟動開發服務器...${NC}"
echo -e "${CYAN}🔧 執行命令: $start_command${NC}"
echo ""

# 記錄啟動嘗試
echo "last_attempt=$(date)" >> .fintranzo_run_history

# 啟動應用
if $start_command; then
    echo "last_success=$(date)" >> .fintranzo_run_history
    echo -e "${GREEN}🎉 FinTranzo 啟動成功！${NC}"
else
    echo "last_failure=$(date)" >> .fintranzo_run_history
    echo -e "${RED}❌ 啟動失敗${NC}"
    echo -e "${YELLOW}💡 建議嘗試:${NC}"
    echo -e "${BLUE}   1. 切換到最小化版本: cp App_minimal.tsx App.tsx${NC}"
    echo -e "${BLUE}   2. 清理並重新安裝: rm -rf node_modules && npm install${NC}"
    echo -e "${BLUE}   3. 重新運行此腳本${NC}"
fi

echo ""
echo -e "${PURPLE}📋 運行完成時間: $(date)${NC}"
