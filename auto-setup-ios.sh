#!/bin/bash

# 🚀 FinTranzo iOS 模擬器全自動化設置腳本
# 作者: Augment Agent
# 用途: 一鍵安裝所有依賴並啟動 iOS 模擬器測試

set -e  # 遇到錯誤立即退出

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 項目路徑
PROJECT_PATH="/Users/<USER>/Documents/開發app/FinTranzo"

echo -e "${PURPLE}🚀 FinTranzo iOS 模擬器全自動化設置${NC}"
echo -e "${PURPLE}===========================================${NC}"
echo -e "${CYAN}📅 開始時間: $(date)${NC}"
echo ""

# 函數：打印步驟標題
print_step() {
    echo -e "${BLUE}📋 步驟 $1: $2${NC}"
    echo -e "${BLUE}----------------------------------------${NC}"
}

# 函數：檢查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 函數：安全執行命令
safe_execute() {
    echo -e "${YELLOW}🔧 執行: $1${NC}"
    if eval "$1"; then
        echo -e "${GREEN}✅ 成功: $1${NC}"
        return 0
    else
        echo -e "${RED}❌ 失敗: $1${NC}"
        return 1
    fi
}

# 步驟 1: 檢查系統環境
print_step "1" "檢查系統環境"

# 檢查 macOS 版本
echo -e "${CYAN}🍎 檢查 macOS 版本...${NC}"
macos_version=$(sw_vers -productVersion)
echo -e "${GREEN}✅ macOS 版本: $macos_version${NC}"

# 檢查 Xcode
echo -e "${CYAN}📱 檢查 Xcode...${NC}"
if xcode-select -p >/dev/null 2>&1; then
    xcode_path=$(xcode-select -p)
    echo -e "${GREEN}✅ Xcode 已安裝: $xcode_path${NC}"
else
    echo -e "${RED}❌ Xcode 未安裝或未配置${NC}"
    echo -e "${YELLOW}請先安裝 Xcode 並運行: sudo xcode-select --install${NC}"
    exit 1
fi

# 步驟 2: 安裝 Homebrew
print_step "2" "安裝 Homebrew"

if command_exists brew; then
    echo -e "${GREEN}✅ Homebrew 已安裝${NC}"
    brew_version=$(brew --version | head -n1)
    echo -e "${GREEN}📦 版本: $brew_version${NC}"
else
    echo -e "${YELLOW}🔧 安裝 Homebrew...${NC}"
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # 添加 Homebrew 到 PATH
    echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
    eval "$(/opt/homebrew/bin/brew shellenv)"
    
    if command_exists brew; then
        echo -e "${GREEN}✅ Homebrew 安裝成功${NC}"
    else
        echo -e "${RED}❌ Homebrew 安裝失敗${NC}"
        exit 1
    fi
fi

# 步驟 3: 安裝 Node.js
print_step "3" "安裝 Node.js"

if command_exists node; then
    node_version=$(node --version)
    echo -e "${GREEN}✅ Node.js 已安裝: $node_version${NC}"
    
    # 檢查版本是否符合要求 (>=20.0.0)
    required_version="20.0.0"
    if node -e "process.exit(process.version.slice(1).split('.').map(Number).reduce((a,b,i)=>a+b*Math.pow(1000,2-i),0) >= '$required_version'.split('.').map(Number).reduce((a,b,i)=>a+b*Math.pow(1000,2-i),0) ? 0 : 1)"; then
        echo -e "${GREEN}✅ Node.js 版本符合要求 (>= $required_version)${NC}"
    else
        echo -e "${YELLOW}⚠️ Node.js 版本過舊，正在更新...${NC}"
        safe_execute "brew upgrade node"
    fi
else
    echo -e "${YELLOW}🔧 安裝 Node.js...${NC}"
    safe_execute "brew install node"
fi

# 驗證 npm
if command_exists npm; then
    npm_version=$(npm --version)
    echo -e "${GREEN}✅ npm 已安裝: $npm_version${NC}"
else
    echo -e "${RED}❌ npm 未找到${NC}"
    exit 1
fi

# 步驟 4: 進入項目目錄並安裝依賴
print_step "4" "安裝項目依賴"

echo -e "${CYAN}📂 進入項目目錄: $PROJECT_PATH${NC}"
cd "$PROJECT_PATH" || {
    echo -e "${RED}❌ 無法進入項目目錄: $PROJECT_PATH${NC}"
    exit 1
}

echo -e "${GREEN}✅ 當前目錄: $(pwd)${NC}"

# 清理 node_modules 和 package-lock.json（如果存在問題）
if [ -d "node_modules" ]; then
    echo -e "${YELLOW}🧹 清理舊的 node_modules...${NC}"
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    echo -e "${YELLOW}🧹 清理舊的 package-lock.json...${NC}"
    rm -f package-lock.json
fi

# 安裝依賴
echo -e "${CYAN}📦 安裝項目依賴...${NC}"
safe_execute "npm install"

# 步驟 5: 安裝 Expo CLI
print_step "5" "安裝 Expo CLI"

if command_exists expo; then
    expo_version=$(expo --version)
    echo -e "${GREEN}✅ Expo CLI 已安裝: $expo_version${NC}"
else
    echo -e "${YELLOW}🔧 安裝 Expo CLI...${NC}"
    safe_execute "npm install -g @expo/cli"
fi

# 步驟 6: 配置 iOS 模擬器
print_step "6" "配置 iOS 模擬器"

echo -e "${CYAN}📱 檢查可用的 iOS 模擬器...${NC}"
simulators=$(xcrun simctl list devices available | grep -E "iPhone|iPad" | head -5)

if [ -z "$simulators" ]; then
    echo -e "${YELLOW}⚠️ 沒有找到可用的模擬器，正在創建...${NC}"
    
    # 獲取最新的 iOS 運行時
    latest_ios=$(xcrun simctl list runtimes | grep iOS | tail -1 | awk '{print $NF}' | tr -d '()')
    
    if [ -n "$latest_ios" ]; then
        echo -e "${CYAN}📱 創建 iPhone 15 模擬器 (iOS $latest_ios)...${NC}"
        safe_execute "xcrun simctl create 'iPhone 15 FinTranzo' 'iPhone 15' '$latest_ios'"
    else
        echo -e "${RED}❌ 無法找到 iOS 運行時${NC}"
        echo -e "${YELLOW}請在 Xcode 中下載 iOS 模擬器${NC}"
    fi
else
    echo -e "${GREEN}✅ 找到可用的模擬器:${NC}"
    echo "$simulators"
fi

# 步驟 7: 測試最小化版本
print_step "7" "測試最小化版本"

echo -e "${CYAN}🧪 當前使用最小化測試版本${NC}"
echo -e "${CYAN}📄 檢查 App.tsx...${NC}"

if [ -f "App.tsx" ]; then
    if grep -q "最小化測試版本" App.tsx; then
        echo -e "${GREEN}✅ 當前使用最小化測試版本（安全）${NC}"
    else
        echo -e "${YELLOW}⚠️ 當前不是最小化版本，正在切換...${NC}"
        if [ -f "App_minimal.tsx" ]; then
            cp App_minimal.tsx App.tsx
            echo -e "${GREEN}✅ 已切換到最小化版本${NC}"
        fi
    fi
else
    echo -e "${RED}❌ App.tsx 文件不存在${NC}"
    exit 1
fi

# 步驟 8: 啟動應用
print_step "8" "啟動 FinTranzo 應用"

echo -e "${PURPLE}🚀 準備啟動 FinTranzo...${NC}"
echo -e "${CYAN}📱 將在 iOS 模擬器中啟動應用${NC}"
echo -e "${YELLOW}⏳ 請稍等，正在啟動開發服務器...${NC}"

# 創建啟動腳本
cat > start_app.sh << 'EOF'
#!/bin/bash
echo "🚀 啟動 FinTranzo iOS 模擬器..."
npx expo start --ios --clear
EOF

chmod +x start_app.sh

echo -e "${GREEN}✅ 自動化設置完成！${NC}"
echo -e "${PURPLE}===========================================${NC}"
echo -e "${GREEN}🎉 FinTranzo 已準備就緒！${NC}"
echo ""
echo -e "${CYAN}📱 接下來將自動啟動 iOS 模擬器...${NC}"
echo -e "${YELLOW}⚠️ 如果模擬器沒有自動打開，請手動運行:${NC}"
echo -e "${BLUE}   cd '$PROJECT_PATH' && npx expo start --ios${NC}"
echo ""
echo -e "${PURPLE}📋 完成時間: $(date)${NC}"

# 自動啟動應用
echo -e "${CYAN}🚀 正在啟動應用...${NC}"
./start_app.sh
