#!/bin/bash

# 🚀 FinTranzo 快速啟動腳本
# 用途: 快速啟動應用，跳過複雜的環境檢查

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

PROJECT_PATH="/Users/<USER>/Documents/開發app/FinTranzo"

echo -e "${PURPLE}🚀 FinTranzo 快速啟動${NC}"
echo -e "${PURPLE}=====================${NC}"
echo -e "${CYAN}📅 時間: $(date)${NC}"
echo ""

# 進入項目目錄
cd "$PROJECT_PATH" || {
    echo -e "${RED}❌ 無法進入項目目錄${NC}"
    exit 1
}

echo -e "${GREEN}✅ 項目目錄: $(pwd)${NC}"

# 檢查基本文件
echo -e "${BLUE}📋 檢查項目文件...${NC}"
required_files=("package.json" "App.tsx" "app.json")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file 不存在${NC}"
        exit 1
    fi
done

# 檢查當前版本
echo -e "${BLUE}📱 檢查應用版本...${NC}"
if grep -q "最小化測試版本" App.tsx; then
    echo -e "${YELLOW}📱 當前使用最小化測試版本（安全）${NC}"
elif grep -q "ErrorBoundary" App.tsx; then
    echo -e "${GREEN}📱 當前使用完整功能版本${NC}"
else
    echo -e "${BLUE}📱 當前使用標準版本${NC}"
fi

# 檢查 node_modules
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 node_modules 不存在，需要安裝依賴${NC}"
    echo -e "${CYAN}請先運行以下命令:${NC}"
    echo -e "${BLUE}  npm install${NC}"
    echo ""
    read -p "是否現在安裝依賴？(y/N): " install_deps
    if [[ $install_deps =~ ^[Yy]$ ]]; then
        echo -e "${CYAN}📦 安裝依賴中...${NC}"
        npm install
        echo -e "${GREEN}✅ 依賴安裝完成${NC}"
    else
        echo -e "${YELLOW}⚠️ 請手動安裝依賴後再運行${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ 依賴已安裝${NC}"
fi

# 檢查關鍵命令
echo -e "${BLUE}🔧 檢查必要工具...${NC}"

# 檢查 node
if command -v node >/dev/null 2>&1; then
    node_version=$(node --version)
    echo -e "${GREEN}✅ Node.js $node_version${NC}"
else
    echo -e "${RED}❌ Node.js 未安裝${NC}"
    echo -e "${YELLOW}請先安裝 Node.js: https://nodejs.org/${NC}"
    exit 1
fi

# 檢查 npx
if command -v npx >/dev/null 2>&1; then
    echo -e "${GREEN}✅ npx 可用${NC}"
else
    echo -e "${RED}❌ npx 不可用${NC}"
    exit 1
fi

# 檢查 Expo CLI
if command -v expo >/dev/null 2>&1; then
    expo_version=$(expo --version)
    echo -e "${GREEN}✅ Expo CLI $expo_version${NC}"
    use_expo_cli=true
else
    echo -e "${YELLOW}⚠️ Expo CLI 未安裝，將使用 npx expo${NC}"
    use_expo_cli=false
fi

# 檢查 iOS 模擬器
echo -e "${BLUE}📱 檢查 iOS 模擬器...${NC}"
if command -v xcrun >/dev/null 2>&1; then
    simulators=$(xcrun simctl list devices available | grep -E "iPhone" | head -3)
    if [ -n "$simulators" ]; then
        echo -e "${GREEN}✅ 找到可用的 iOS 模擬器${NC}"
        echo "$simulators" | head -2
    else
        echo -e "${YELLOW}⚠️ 沒有找到可用的 iOS 模擬器${NC}"
        echo -e "${CYAN}💡 應用仍可啟動，但需要手動選擇模擬器${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ Xcode 命令行工具不可用${NC}"
fi

# 啟動選項
echo ""
echo -e "${CYAN}🚀 選擇啟動方式:${NC}"
echo -e "${BLUE}1) iOS 模擬器 (推薦)${NC}"
echo -e "${BLUE}2) Web 瀏覽器${NC}"
echo -e "${BLUE}3) 開發服務器 (手動選擇)${NC}"
echo ""

read -p "請選擇 (1-3) [默認: 1]: " start_option
start_option=${start_option:-1}

# 準備啟動命令
case $start_option in
    1)
        if $use_expo_cli; then
            start_cmd="expo start --ios --clear"
        else
            start_cmd="npx expo start --ios --clear"
        fi
        echo -e "${CYAN}📱 將在 iOS 模擬器中啟動${NC}"
        ;;
    2)
        if $use_expo_cli; then
            start_cmd="expo start --web --clear"
        else
            start_cmd="npx expo start --web --clear"
        fi
        echo -e "${CYAN}🌐 將在 Web 瀏覽器中啟動${NC}"
        ;;
    3)
        if $use_expo_cli; then
            start_cmd="expo start --clear"
        else
            start_cmd="npx expo start --clear"
        fi
        echo -e "${CYAN}🔧 將啟動開發服務器，請手動選擇平台${NC}"
        ;;
    *)
        echo -e "${YELLOW}⚠️ 無效選項，使用默認 iOS 模擬器${NC}"
        if $use_expo_cli; then
            start_cmd="expo start --ios --clear"
        else
            start_cmd="npx expo start --ios --clear"
        fi
        ;;
esac

# 最終確認
echo ""
echo -e "${PURPLE}🎯 準備執行:${NC}"
echo -e "${BLUE}   $start_cmd${NC}"
echo ""

read -p "按 Enter 開始啟動，或 Ctrl+C 取消..."

# 啟動應用
echo -e "${YELLOW}⏳ 正在啟動 FinTranzo...${NC}"
echo -e "${CYAN}🔧 執行命令: $start_cmd${NC}"
echo ""

# 記錄啟動
echo "quick_start=$(date)" >> .fintranzo_run_history

# 執行啟動命令
if $start_cmd; then
    echo "quick_start_success=$(date)" >> .fintranzo_run_history
    echo -e "${GREEN}🎉 FinTranzo 啟動成功！${NC}"
else
    echo "quick_start_failure=$(date)" >> .fintranzo_run_history
    echo -e "${RED}❌ 啟動失敗${NC}"
    echo ""
    echo -e "${YELLOW}💡 故障排除建議:${NC}"
    echo -e "${BLUE}1. 檢查網絡連接${NC}"
    echo -e "${BLUE}2. 重新安裝依賴: rm -rf node_modules && npm install${NC}"
    echo -e "${BLUE}3. 切換到最小化版本: cp App_minimal.tsx App.tsx${NC}"
    echo -e "${BLUE}4. 嘗試 Web 版本: npx expo start --web${NC}"
    echo ""
    exit 1
fi

echo ""
echo -e "${PURPLE}📋 啟動完成: $(date)${NC}"
