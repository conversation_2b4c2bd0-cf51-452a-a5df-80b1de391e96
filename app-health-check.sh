#!/bin/bash

# 🔍 FinTranzo 應用健康檢查腳本
# 用途: 檢查應用狀態，診斷潛在問題，確保穩定運行

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

PROJECT_PATH="/Users/<USER>/Documents/開發app/FinTranzo"

echo -e "${PURPLE}🔍 FinTranzo 應用健康檢查${NC}"
echo -e "${PURPLE}================================${NC}"
echo -e "${CYAN}📅 檢查時間: $(date)${NC}"
echo ""

# 進入項目目錄
cd "$PROJECT_PATH" || exit 1

# 檢查項目結構
echo -e "${BLUE}📂 檢查項目結構...${NC}"
required_files=("package.json" "app.json" "App.tsx" "src" "assets")
missing_files=()

for file in "${required_files[@]}"; do
    if [ -e "$file" ]; then
        echo -e "${GREEN}✅ $file${NC}"
    else
        echo -e "${RED}❌ $file${NC}"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo -e "${RED}❌ 缺少必要文件: ${missing_files[*]}${NC}"
    exit 1
fi

# 檢查 package.json
echo -e "${BLUE}📦 檢查 package.json...${NC}"
if node -e "
const pkg = require('./package.json');
console.log('✅ 應用名稱:', pkg.name);
console.log('✅ 版本:', pkg.version);
console.log('✅ 主要依賴:');
const mainDeps = ['expo', 'react', 'react-native', '@supabase/supabase-js'];
mainDeps.forEach(dep => {
    if (pkg.dependencies[dep]) {
        console.log('  ✅', dep + ':', pkg.dependencies[dep]);
    } else {
        console.log('  ❌', dep + ': 未安裝');
    }
});
"; then
    echo -e "${GREEN}✅ package.json 檢查通過${NC}"
else
    echo -e "${RED}❌ package.json 檢查失敗${NC}"
fi

# 檢查 node_modules
echo -e "${BLUE}📚 檢查依賴安裝...${NC}"
if [ -d "node_modules" ]; then
    node_modules_size=$(du -sh node_modules | cut -f1)
    echo -e "${GREEN}✅ node_modules 存在 (大小: $node_modules_size)${NC}"
    
    # 檢查關鍵依賴
    critical_deps=("expo" "react" "react-native" "@supabase/supabase-js" "@react-navigation/native")
    for dep in "${critical_deps[@]}"; do
        if [ -d "node_modules/$dep" ]; then
            echo -e "${GREEN}✅ $dep${NC}"
        else
            echo -e "${RED}❌ $dep 未安裝${NC}"
        fi
    done
else
    echo -e "${RED}❌ node_modules 不存在，需要運行 npm install${NC}"
fi

# 檢查 App.tsx 版本
echo -e "${BLUE}📱 檢查 App.tsx 版本...${NC}"
if grep -q "最小化測試版本" App.tsx; then
    echo -e "${YELLOW}⚠️ 當前使用最小化測試版本${NC}"
    echo -e "${CYAN}💡 這是為了避免閃退問題的安全版本${NC}"
elif grep -q "ErrorBoundary" App.tsx; then
    echo -e "${GREEN}✅ 當前使用完整版本（帶錯誤邊界）${NC}"
else
    echo -e "${BLUE}ℹ️ 當前使用標準版本${NC}"
fi

# 檢查備份文件
echo -e "${BLUE}💾 檢查備份文件...${NC}"
backup_files=("App_backup.tsx" "App_minimal.tsx")
for backup in "${backup_files[@]}"; do
    if [ -f "$backup" ]; then
        echo -e "${GREEN}✅ $backup 存在${NC}"
    else
        echo -e "${YELLOW}⚠️ $backup 不存在${NC}"
    fi
done

# 檢查環境配置
echo -e "${BLUE}🔧 檢查環境配置...${NC}"
if [ -f ".env" ]; then
    echo -e "${GREEN}✅ .env 文件存在${NC}"
    # 檢查關鍵環境變量（不顯示實際值）
    env_vars=("EXPO_PUBLIC_SUPABASE_URL" "EXPO_PUBLIC_SUPABASE_ANON_KEY")
    for var in "${env_vars[@]}"; do
        if grep -q "$var" .env; then
            echo -e "${GREEN}✅ $var 已配置${NC}"
        else
            echo -e "${YELLOW}⚠️ $var 未配置${NC}"
        fi
    done
else
    echo -e "${YELLOW}⚠️ .env 文件不存在${NC}"
fi

# 檢查 iOS 配置
echo -e "${BLUE}📱 檢查 iOS 配置...${NC}"
if node -e "
const config = require('./app.json');
const ios = config.expo.ios;
console.log('✅ Bundle ID:', ios.bundleIdentifier);
console.log('✅ Build Number:', ios.buildNumber);
console.log('✅ JS Engine:', ios.jsEngine);
console.log('✅ New Arch:', config.expo.newArchEnabled ? 'Enabled' : 'Disabled');
"; then
    echo -e "${GREEN}✅ iOS 配置檢查通過${NC}"
fi

# 檢查潛在問題
echo -e "${BLUE}🔍 檢查潛在問題...${NC}"

# 檢查是否有衝突的依賴
echo -e "${CYAN}🔍 檢查依賴衝突...${NC}"
if npm ls --depth=0 2>/dev/null | grep -q "UNMET DEPENDENCY\|missing"; then
    echo -e "${RED}❌ 發現依賴問題${NC}"
    echo -e "${YELLOW}💡 建議運行: npm install --force${NC}"
else
    echo -e "${GREEN}✅ 依賴檢查通過${NC}"
fi

# 檢查 Metro 配置
if [ -f "metro.config.js" ]; then
    echo -e "${GREEN}✅ Metro 配置存在${NC}"
else
    echo -e "${YELLOW}⚠️ Metro 配置不存在${NC}"
fi

# 生成健康報告
echo -e "${PURPLE}📊 健康檢查報告${NC}"
echo -e "${PURPLE}==================${NC}"

# 計算健康分數
total_checks=10
passed_checks=0

[ -f "package.json" ] && ((passed_checks++))
[ -d "node_modules" ] && ((passed_checks++))
[ -f "App.tsx" ] && ((passed_checks++))
[ -f "app.json" ] && ((passed_checks++))
[ -d "src" ] && ((passed_checks++))
[ -d "assets" ] && ((passed_checks++))
[ -f "App_backup.tsx" ] && ((passed_checks++))
[ -f "metro.config.js" ] && ((passed_checks++))
command -v node >/dev/null 2>&1 && ((passed_checks++))
command -v expo >/dev/null 2>&1 && ((passed_checks++))

health_score=$((passed_checks * 100 / total_checks))

echo -e "${CYAN}🏥 健康分數: $health_score/100${NC}"

if [ $health_score -ge 90 ]; then
    echo -e "${GREEN}🎉 應用狀態優秀！可以安全運行${NC}"
elif [ $health_score -ge 70 ]; then
    echo -e "${YELLOW}⚠️ 應用狀態良好，但有一些小問題${NC}"
else
    echo -e "${RED}❌ 應用狀態需要改善${NC}"
fi

# 提供建議
echo -e "${BLUE}💡 建議操作:${NC}"
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}1. 運行 npm install 安裝依賴${NC}"
fi

if ! grep -q "最小化測試版本" App.tsx; then
    echo -e "${YELLOW}2. 如果遇到閃退，切換到最小化版本: cp App_minimal.tsx App.tsx${NC}"
fi

echo -e "${CYAN}3. 運行應用: npx expo start --ios${NC}"
echo -e "${CYAN}4. 如果需要清理: rm -rf node_modules && npm install${NC}"

echo ""
echo -e "${PURPLE}📋 檢查完成時間: $(date)${NC}"
