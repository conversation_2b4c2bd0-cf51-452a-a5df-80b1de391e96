#!/bin/bash

# 🚀 FinTranzo 簡化安裝腳本
# 用途: 使用官方安裝器，避免需要 sudo 權限

set -e

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

PROJECT_PATH="/Users/<USER>/Documents/開發app/FinTranzo"

echo -e "${PURPLE}🚀 FinTranzo 簡化安裝${NC}"
echo -e "${PURPLE}=====================${NC}"
echo -e "${CYAN}📅 時間: $(date)${NC}"
echo ""

# 進入項目目錄
cd "$PROJECT_PATH" || exit 1
echo -e "${GREEN}✅ 項目目錄: $(pwd)${NC}"

# 檢查是否已有 Node.js
if command -v node >/dev/null 2>&1; then
    node_version=$(node --version)
    echo -e "${GREEN}✅ Node.js 已安裝: $node_version${NC}"
    skip_node_install=true
else
    echo -e "${YELLOW}📦 需要安裝 Node.js${NC}"
    skip_node_install=false
fi

# 如果沒有 Node.js，提供安裝選項
if [ "$skip_node_install" = false ]; then
    echo -e "${CYAN}選擇 Node.js 安裝方式:${NC}"
    echo -e "${BLUE}1) 使用官方安裝器 (推薦，無需密碼)${NC}"
    echo -e "${BLUE}2) 使用 Homebrew (需要密碼)${NC}"
    echo -e "${BLUE}3) 手動下載安裝${NC}"
    echo ""
    
    read -p "請選擇 (1-3) [默認: 1]: " install_choice
    install_choice=${install_choice:-1}
    
    case $install_choice in
        1)
            echo -e "${CYAN}🌐 正在下載 Node.js 官方安裝器...${NC}"
            
            # 檢測系統架構
            if [[ $(uname -m) == "arm64" ]]; then
                node_url="https://nodejs.org/dist/v20.11.0/node-v20.11.0.pkg"
                echo -e "${BLUE}📱 檢測到 Apple Silicon (M1/M2)${NC}"
            else
                node_url="https://nodejs.org/dist/v20.11.0/node-v20.11.0.pkg"
                echo -e "${BLUE}💻 檢測到 Intel 處理器${NC}"
            fi
            
            # 下載安裝器
            curl -o /tmp/nodejs-installer.pkg "$node_url"
            
            echo -e "${YELLOW}📦 請在彈出的安裝器中完成 Node.js 安裝${NC}"
            echo -e "${CYAN}💡 安裝完成後，請重新運行此腳本${NC}"
            
            # 打開安裝器
            open /tmp/nodejs-installer.pkg
            
            echo -e "${BLUE}⏳ 等待安裝完成...${NC}"
            read -p "安裝完成後按 Enter 繼續..."
            
            # 重新檢查
            if command -v node >/dev/null 2>&1; then
                node_version=$(node --version)
                echo -e "${GREEN}✅ Node.js 安裝成功: $node_version${NC}"
            else
                echo -e "${RED}❌ Node.js 安裝失敗，請手動安裝${NC}"
                echo -e "${CYAN}請訪問: https://nodejs.org/zh-cn/download/${NC}"
                exit 1
            fi
            ;;
        2)
            echo -e "${YELLOW}🍺 使用 Homebrew 安裝...${NC}"
            if ! command -v brew >/dev/null 2>&1; then
                echo -e "${CYAN}📦 安裝 Homebrew...${NC}"
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            brew install node
            ;;
        3)
            echo -e "${CYAN}🌐 請手動下載並安裝 Node.js${NC}"
            echo -e "${BLUE}下載地址: https://nodejs.org/zh-cn/download/${NC}"
            echo -e "${YELLOW}安裝完成後請重新運行此腳本${NC}"
            open "https://nodejs.org/zh-cn/download/"
            exit 0
            ;;
        *)
            echo -e "${RED}❌ 無效選項${NC}"
            exit 1
            ;;
    esac
fi

# 檢查 npm
if command -v npm >/dev/null 2>&1; then
    npm_version=$(npm --version)
    echo -e "${GREEN}✅ npm 可用: $npm_version${NC}"
else
    echo -e "${RED}❌ npm 不可用${NC}"
    exit 1
fi

# 安裝項目依賴
echo -e "${BLUE}📦 安裝項目依賴...${NC}"
if [ ! -d "node_modules" ]; then
    echo -e "${CYAN}🔧 運行 npm install...${NC}"
    npm install
    echo -e "${GREEN}✅ 依賴安裝完成${NC}"
else
    echo -e "${GREEN}✅ 依賴已存在${NC}"
fi

# 安裝 Expo CLI
echo -e "${BLUE}📱 檢查 Expo CLI...${NC}"
if ! command -v expo >/dev/null 2>&1; then
    echo -e "${CYAN}🔧 安裝 Expo CLI...${NC}"
    npm install -g @expo/cli
    echo -e "${GREEN}✅ Expo CLI 安裝完成${NC}"
else
    expo_version=$(expo --version)
    echo -e "${GREEN}✅ Expo CLI 已安裝: $expo_version${NC}"
fi

# 檢查應用版本
echo -e "${BLUE}📱 檢查應用版本...${NC}"
if grep -q "最小化測試版本" App.tsx; then
    echo -e "${YELLOW}📱 當前使用最小化測試版本（安全）${NC}"
    current_version="minimal"
elif grep -q "ErrorBoundary" App.tsx; then
    echo -e "${GREEN}📱 當前使用完整功能版本${NC}"
    current_version="full"
else
    echo -e "${BLUE}📱 當前使用標準版本${NC}"
    current_version="standard"
fi

# 版本建議
echo -e "${CYAN}💡 建議首次運行使用最小化版本測試${NC}"
if [ "$current_version" != "minimal" ]; then
    read -p "是否切換到最小化版本？(Y/n): " switch_minimal
    if [[ ! $switch_minimal =~ ^[Nn]$ ]]; then
        if [ -f "App_minimal.tsx" ]; then
            cp App_minimal.tsx App.tsx
            echo -e "${GREEN}✅ 已切換到最小化版本${NC}"
        else
            echo -e "${YELLOW}⚠️ 最小化版本文件不存在${NC}"
        fi
    fi
fi

# 啟動應用
echo -e "${PURPLE}🚀 準備啟動 FinTranzo${NC}"
echo -e "${CYAN}📱 將在 iOS 模擬器中啟動應用${NC}"
echo ""

read -p "按 Enter 開始啟動..."

echo -e "${YELLOW}⏳ 正在啟動開發服務器...${NC}"
echo -e "${CYAN}🔧 執行: npx expo start --ios --clear${NC}"
echo ""

# 記錄啟動
echo "simple_install_start=$(date)" >> .fintranzo_run_history

# 啟動應用
if npx expo start --ios --clear; then
    echo "simple_install_success=$(date)" >> .fintranzo_run_history
    echo -e "${GREEN}🎉 FinTranzo 啟動成功！${NC}"
else
    echo "simple_install_failure=$(date)" >> .fintranzo_run_history
    echo -e "${RED}❌ 啟動失敗${NC}"
    echo ""
    echo -e "${YELLOW}💡 故障排除:${NC}"
    echo -e "${BLUE}1. 檢查 Xcode 是否已安裝${NC}"
    echo -e "${BLUE}2. 嘗試 Web 版本: npx expo start --web${NC}"
    echo -e "${BLUE}3. 檢查網絡連接${NC}"
    exit 1
fi

echo ""
echo -e "${PURPLE}📋 安裝完成: $(date)${NC}"
